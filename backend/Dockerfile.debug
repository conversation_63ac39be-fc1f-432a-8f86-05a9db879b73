FROM node:18-alpine

WORKDIR /usr/src/app

# Test basic commands first
RUN echo "Testing basic commands"
RUN ls -la
RUN node --version
RUN npm --version

COPY package*.json ./

# Try to debug npm install
RUN echo "About to run npm install"
RUN npm config list
RUN npm install --verbose --production

COPY dist ./dist
COPY apiclient_cert.pem ./apiclient_cert.pem
COPY apiclient_key.pem ./apiclient_key.pem

RUN mkdir -p logs && chmod 777 logs

CMD ["node", "dist/index"]
